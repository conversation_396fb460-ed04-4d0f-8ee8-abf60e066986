"""安全基线迁移 - 现有数据库结构

修订ID: 48ddafa6ed76
修订时间: 2025-07-31 13:59:09.597168
"""
from alembic import op
import sqlalchemy as sa

# 修订标识符，由Alembic使用
revision = '48ddafa6ed76'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构
    
    这是一个安全的基线迁移，不会对现有数据库结构进行任何更改。
    它仅用于将现有数据库纳入Alembic管理体系。
    """
    # 这是一个空的迁移，不执行任何操作
    # 现有的数据库结构已经是正确的
    pass


def downgrade() -> None:
    """降级数据库结构
    
    基线迁移不支持降级操作
    """
    # 基线迁移不支持降级
    raise NotImplementedError("基线迁移不支持降级操作")
