"""
智能体相关的Pydantic模式定义
"""
from datetime import datetime
from typing import Optional, Dict, Any, List, Literal
from pydantic import BaseModel, Field, validator

from .report import ReportPetInfo
from ..models.agent import AgentType, AgentStatus, SystemAgentCategory


# 基础模式
class AgentBase(BaseModel):
    """智能体基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="智能体名称")
    display_name: str = Field(..., min_length=1,
                              max_length=200, description="显示名称")
    description: Optional[str] = Field(None, description="智能体描述")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    agent_type: AgentType = Field(..., description="智能体类型")
    system_category: Optional[SystemAgentCategory] = Field(
        None, description="系统智能体分类")
    is_public: bool = Field(default=False, description="是否公开")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")
    prompt_template: Optional[str] = Field(None, description="提示词模板")

    @validator('system_category')
    def validate_system_category(cls, v, values):
        """验证系统智能体分类"""
        agent_type = values.get('agent_type')
        if agent_type == AgentType.SYSTEM and v is None:
            raise ValueError('系统智能体必须指定分类')
        if agent_type == AgentType.CUSTOM and v is not None:
            raise ValueError('用户自定义智能体不能指定系统分类')
        return v


# 创建模式
class AgentCreate(AgentBase):
    """创建智能体的请求模式"""
    pass


# 更新模式
class AgentUpdate(BaseModel):
    """更新智能体的请求模式"""
    display_name: Optional[str] = Field(
        None, min_length=1, max_length=200, description="显示名称")
    description: Optional[str] = Field(None, description="智能体描述")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    is_public: Optional[bool] = Field(None, description="是否公开")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")
    prompt_template: Optional[str] = Field(None, description="提示词模板")
    status: Optional[AgentStatus] = Field(None, description="状态")


# 响应模式
class AgentResponse(AgentBase):
    """智能体响应模式"""
    id: int
    status: AgentStatus

    owner_id: Optional[int]
    usage_count: int
    last_used_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        """从数据库对象创建响应模式"""
        # 将数据库字段 agent_name 映射到 schema 字段 name
        data = {
            'name': obj.agent_name,
            'display_name': obj.display_name,
            'description': obj.description,
            'avatar_url': obj.avatar_url,
            'agent_type': obj.agent_type,
            'system_category': obj.system_category,
            'is_public': obj.is_public,
            'config': obj.config,
            'prompt_template': obj.prompt_template,
            'id': obj.id,
            'status': obj.status,
            'owner_id': obj.owner_id,
            'usage_count': obj.usage_count,
            'last_used_at': obj.last_used_at,
            'created_at': obj.created_at,
            'updated_at': obj.updated_at
        }
        return cls(**data)


# 智能体列表响应
class AgentListResponse(BaseModel):
    """智能体列表响应模式"""
    agents: List[AgentResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# 权限相关模式
class AgentPermissionBase(BaseModel):
    """智能体权限基础模式"""
    user_id: int = Field(..., description="用户ID")
    permission_type: str = Field(..., description="权限类型")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class AgentPermissionCreate(AgentPermissionBase):
    """创建权限的请求模式"""
    pass


class AgentPermissionResponse(AgentPermissionBase):
    """权限响应模式"""
    id: int
    agent_id: int
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


# 执行相关模式
class AgentExecutionRequest(BaseModel):
    """智能体执行请求模式"""
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    stream: bool = Field(default=False, description="是否流式响应")
    config_override: Optional[Dict[str, Any]] = Field(None, description="配置覆盖")
    enable_short_term_memory: Optional[bool] = Field(None, description="是否启用短期记忆，None时使用默认值")


class AgentExecutionResponse(BaseModel):
    """智能体执行响应模式"""
    execution_id: str = Field(..., description="执行ID")
    status: str = Field(..., description="执行状态")
    output_data: Optional[Dict[str, Any]] = Field(None, description="输出数据")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    duration_ms: Optional[int] = Field(None, description="执行时长")
    token_usage: Optional[Dict[str, Any]] = Field(
        None, description="Token使用统计")
    error_message: Optional[str] = Field(None, description="错误信息")

    class Config:
        from_attributes = True


# 灵活的消息模式
class ChatMessage(BaseModel):
    """聊天消息模式"""
    role: str = Field(..., description="消息角色: system, user, assistant")
    content: str = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")


# 灵活的智能体输入模式
class FlexibleAgentInput(BaseModel):
    """灵活的智能体输入模式 - 支持多种输入格式"""
    # OpenAI风格的messages输入
    messages: Optional[List[ChatMessage]] = Field(None, description="对话消息列表")

    # 直接文本输入
    prompt: Optional[str] = Field(None, description="直接提示文本")

    # 结构化输入（向后兼容）
    structured_input: Optional[Dict[str, Any]] = Field(
        None, description="结构化输入数据")

    # 执行参数
    temperature: Optional[float] = Field(0.7, ge=0, le=2, description="温度参数")
    max_tokens: Optional[int] = Field(None, ge=1, description="最大token数")
    stream: Optional[bool] = Field(False, description="是否流式输出")

    @validator('stream', pre=False, always=True)
    def validate_input_provided(cls, v, values):
        """确保至少提供一种输入方式"""
        messages = values.get('messages')
        prompt = values.get('prompt')
        structured_input = values.get('structured_input')

        if not any([messages, prompt, structured_input]):
            raise ValueError("必须提供messages、prompt或structured_input中的至少一种输入方式")
        return v

    @validator('messages')
    def validate_messages_format(cls, v):
        """验证messages格式"""
        if v is not None:
            for msg in v:
                if not isinstance(msg, (dict, ChatMessage)):
                    raise ValueError("messages中的每个元素必须是有效的消息格式")
                if isinstance(msg, dict):
                    if 'role' not in msg or 'content' not in msg:
                        raise ValueError("消息必须包含role和content字段")
        return v


# 系统智能体特定模式（保留向后兼容）
class DiagnosisAgentInput(BaseModel):
    """AI问诊智能体输入模式（传统格式，保留向后兼容）"""
    pet_info: Dict[str, Any] = Field(..., description="宠物信息")
    symptoms: List[str] = Field(..., description="症状列表")
    additional_info: Optional[str] = Field(None, description="补充信息")


class VisionAgentInput(BaseModel):
    """AI视觉识别智能体输入模式"""
    image_urls: List[str] = Field(..., description="图片URL列表")
    analysis_type: str = Field(..., description="分析类型")
    additional_context: Optional[str] = Field(None, description="额外上下文")

ReportDataType = Literal["xiao_pei", "ping_an"]

class ReportGenerationAgentInput(BaseModel):
    """AI报告生成智能体输入模式"""
    pet_id: Optional[int] = Field(None, description="宠物ID")
    pet_data: Optional[ReportPetInfo] = Field(None, description="宠物数据")
    report_type: str = Field(..., description="报告类型")
    nutritional_diet_target: Optional[str] = Field(None, description="营养饮食目标")
    deworming_plan_target: Optional[str] = Field(None, description="驱虫目标")
    data_type: Optional[ReportDataType] = Field(None, description="数据类型")


class ReportAnalysisAgentInput(BaseModel):
    """AI报告解读智能体输入模式"""
    report_files: List[str] = Field(..., description="报告文件URL列表")
    file_type: str = Field(..., description="文件类型: pdf, image")
    analysis_focus: Optional[List[str]] = Field(None, description="分析重点")


# 查询参数模式
class AgentQueryParams(BaseModel):
    """智能体查询参数模式"""
    agent_type: Optional[AgentType] = Field(None, description="智能体类型")
    system_category: Optional[SystemAgentCategory] = Field(
        None, description="系统分类")
    status: Optional[AgentStatus] = Field(None, description="状态")
    is_public: Optional[bool] = Field(None, description="是否公开")
    owner_id: Optional[int] = Field(None, description="所有者ID")
    search: Optional[str] = Field(None, description="搜索关键词")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页数量")


# 统计信息模式
class AgentStatsResponse(BaseModel):
    """智能体统计信息响应模式"""
    total_agents: int = Field(..., description="总智能体数量")
    system_agents: int = Field(..., description="系统智能体数量")
    custom_agents: int = Field(..., description="自定义智能体数量")
    active_agents: int = Field(..., description="激活智能体数量")
    total_executions: int = Field(..., description="总执行次数")
    executions_today: int = Field(..., description="今日执行次数")
    popular_agents: List[Dict[str, Any]] = Field(..., description="热门智能体")
