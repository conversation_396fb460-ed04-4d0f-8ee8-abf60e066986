"""
宠物相关的Pydantic模式
"""
from datetime import datetime, date
from typing import Optional, List, Union, Literal

from pydantic import BaseModel, Field, ConfigDict

from ..models.pet import PetGender, PetStatus


# ============ 其他信息专用模式 ============

class BetaCatOtherInfo(BaseModel):
    """贝塔猫医疗检查其他信息模式"""
    data_type: Literal["beta_cat"] = "beta_cat"
    pet_symptoms: Optional[str] = Field(None, description="宠物症状信息")
    context: Optional[str] = Field(None, description="宠物病史信息")
    pet_signs: Optional[str] = Field(None, description="宠物的体征信息")
    pet_spirit: Optional[str] = Field(None, description="宠物精神状态")
    pet_visual: Optional[str] = Field(None, description="可视粘膜颜色")
    pet_oral: Optional[str] = Field(None, description="口腔情况")
    pet_skin: Optional[str] = Field(None, description="皮肤情况")
    pet_heart: Optional[str] = Field(None, description="心音听诊")
    pet_abdominal: Optional[str] = Field(None, description="腹部触诊")
    pet_lymph: Optional[str] = Field(None, description="淋巴结检查")


class XiaoPeiOtherInfo(BaseModel):
    """小佩猫砂盆监测其他信息模式"""
    data_type: Literal["xiao_pei"] = "xiao_pei"
    litter: Optional[str] = Field(None, max_length=100, description="猫砂类型")
    time_per_use: Optional[int] = Field(None, ge=0, le=3600, description="单次使用时长(秒)")
    times_per_day: Optional[int] = Field(None, ge=0, le=50, description="每日使用次数")
    poop_times: Optional[int] = Field(None, ge=0, le=20, description="排便次数")
    pee_times: Optional[int] = Field(None, ge=0, le=30, description="排尿次数")
    soft_poop: Optional[bool] = Field(None, description="是否软便")
    amount: Optional[float] = Field(None, ge=0, le=1000, description="排泄物重量(克)")
    pee_ph: Optional[float] = Field(None, ge=0, le=14, description="尿液pH值")
    note: Optional[str] = Field(None, max_length=500, description="备注信息")


# 基础模式
class PetBase(BaseModel):
    """宠物基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="宠物姓名")
    species: str = Field(..., min_length=1, max_length=50, description="物种")
    breed: Optional[str] = Field(None, max_length=100, description="品种")
    gender: PetGender = Field(default=PetGender.UNKNOWN, description="性别")
    birth_date: Optional[date] = Field(None, description="出生日期")
    age_years: Optional[int] = Field(None, ge=0, le=50, description="年龄(年)")
    age_months: Optional[int] = Field(None, ge=0, le=11, description="年龄(月)")
    color: Optional[str] = Field(None, max_length=100, description="毛色")
    weight: Optional[float] = Field(None, ge=0, le=200, description="体重(kg)")
    height: Optional[float] = Field(None, ge=0, le=200, description="身高(cm)")
    microchip_id: Optional[str] = Field(None, max_length=50, description="芯片号")
    registration_number: Optional[str] = Field(None, max_length=100, description="注册号")
    status: PetStatus = Field(default=PetStatus.ACTIVE, description="状态")
    is_neutered: bool = Field(default=False, description="是否绝育")
    is_vaccinated: bool = Field(default=False, description="是否接种疫苗")
    description: Optional[str] = Field(None, description="描述")
    special_needs: Optional[str] = Field(None, description="特殊需求")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    other_info: Optional[Union[BetaCatOtherInfo, XiaoPeiOtherInfo]] = Field(
        None, description="其他补充信息(支持贝塔猫医疗检查、小佩猫砂盆监测或自定义JSON格式)"
    )


class PetCreate(PetBase):
    """创建宠物模式"""
    owner_id: int = Field(..., description="主人ID")
    tenant_pet_id: Optional[str] = Field(None, max_length=128, description="厂商的宠物ID")


class PetUpdate(BaseModel):
    """更新宠物模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="宠物姓名")
    species: Optional[str] = Field(None, min_length=1, max_length=50, description="物种")
    breed: Optional[str] = Field(None, max_length=100, description="品种")
    gender: Optional[PetGender] = Field(None, description="性别")
    birth_date: Optional[date] = Field(None, description="出生日期")
    age_years: Optional[int] = Field(None, ge=0, le=50, description="年龄(年)")
    age_months: Optional[int] = Field(None, ge=0, le=11, description="年龄(月)")
    color: Optional[str] = Field(None, max_length=100, description="毛色")
    weight: Optional[float] = Field(None, ge=0, le=200, description="体重(kg)")
    height: Optional[float] = Field(None, ge=0, le=200, description="身高(cm)")
    microchip_id: Optional[str] = Field(None, max_length=50, description="芯片号")
    registration_number: Optional[str] = Field(None, max_length=100, description="注册号")
    status: Optional[PetStatus] = Field(None, description="状态")
    is_neutered: Optional[bool] = Field(None, description="是否绝育")
    is_vaccinated: Optional[bool] = Field(None, description="是否接种疫苗")
    description: Optional[str] = Field(None, description="描述")
    special_needs: Optional[str] = Field(None, description="特殊需求")
    avatar_url: Optional[str] = Field(None, max_length=500, description="头像URL")
    other_info: Optional[Union[BetaCatOtherInfo, XiaoPeiOtherInfo]] = Field(
        None, description="其他补充信息(支持贝塔猫医疗检查、小佩猫砂盆监测或自定义JSON格式)"
    )
    tenant_pet_id: Optional[str] = Field(None, max_length=128, description="厂商的宠物ID")
    owner_id: Optional[int] = Field(None, description="主人ID")


class Pet(PetBase):
    """宠物响应模式"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    owner_id: int
    tenant_pet_id: Optional[str] = Field(None, max_length=128, description="厂商的宠物ID")
    age_display: str
    created_at: datetime
    updated_at: datetime


class PetList(BaseModel):
    """宠物列表响应模式"""
    items: List[Pet]
    total: int
    page: int
    size: int
    pages: int


# 医疗记录模式
class MedicalRecordBase(BaseModel):
    """医疗记录基础模式"""
    visit_date: datetime = Field(..., description="就诊日期")
    diagnosis: str = Field(..., min_length=1, description="诊断")
    symptoms: Optional[str] = Field(None, description="症状")
    treatment: Optional[str] = Field(None, description="治疗方案")
    veterinarian_name: Optional[str] = Field(None, max_length=100, description="兽医姓名")
    clinic_name: Optional[str] = Field(None, max_length=200, description="诊所名称")
    cost: Optional[float] = Field(None, ge=0, description="费用")
    notes: Optional[str] = Field(None, description="备注")


class MedicalRecordCreate(MedicalRecordBase):
    """创建医疗记录模式"""
    pet_id: int = Field(..., description="宠物ID")


class MedicalRecordUpdate(BaseModel):
    """更新医疗记录模式"""
    visit_date: Optional[datetime] = Field(None, description="就诊日期")
    diagnosis: Optional[str] = Field(None, min_length=1, description="诊断")
    symptoms: Optional[str] = Field(None, description="症状")
    treatment: Optional[str] = Field(None, description="治疗方案")
    veterinarian_name: Optional[str] = Field(None, max_length=100, description="兽医姓名")
    clinic_name: Optional[str] = Field(None, max_length=200, description="诊所名称")
    cost: Optional[float] = Field(None, ge=0, description="费用")
    notes: Optional[str] = Field(None, description="备注")


class MedicalRecord(MedicalRecordBase):
    """医疗记录响应模式"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    pet_id: int
    created_at: datetime
    updated_at: datetime


class MedicalRecordList(BaseModel):
    """医疗记录列表响应模式"""
    items: List[MedicalRecord]
    total: int
    page: int
    size: int
    pages: int


# 疫苗接种记录模式
class VaccinationBase(BaseModel):
    """疫苗接种记录基础模式"""
    vaccine_name: str = Field(..., min_length=1, max_length=100, description="疫苗名称")
    vaccine_type: Optional[str] = Field(None, max_length=50, description="疫苗类型")
    batch_number: Optional[str] = Field(None, max_length=100, description="批次号")
    vaccination_date: date = Field(..., description="接种日期")
    next_due_date: Optional[date] = Field(None, description="下次接种日期")
    veterinarian_name: Optional[str] = Field(None, max_length=100, description="兽医姓名")
    clinic_name: Optional[str] = Field(None, max_length=200, description="诊所名称")
    notes: Optional[str] = Field(None, description="备注")


class VaccinationCreate(VaccinationBase):
    """创建疫苗接种记录模式"""
    pet_id: int = Field(..., description="宠物ID")


class VaccinationUpdate(BaseModel):
    """更新疫苗接种记录模式"""
    vaccine_name: Optional[str] = Field(None, min_length=1, max_length=100, description="疫苗名称")
    vaccine_type: Optional[str] = Field(None, max_length=50, description="疫苗类型")
    batch_number: Optional[str] = Field(None, max_length=100, description="批次号")
    vaccination_date: Optional[date] = Field(None, description="接种日期")
    next_due_date: Optional[date] = Field(None, description="下次接种日期")
    veterinarian_name: Optional[str] = Field(None, max_length=100, description="兽医姓名")
    clinic_name: Optional[str] = Field(None, max_length=200, description="诊所名称")
    notes: Optional[str] = Field(None, description="备注")


class Vaccination(VaccinationBase):
    """疫苗接种记录响应模式"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    pet_id: int
    created_at: datetime
    updated_at: datetime


class VaccinationList(BaseModel):
    """疫苗接种记录列表响应模式"""
    items: List[Vaccination]
    total: int
    page: int
    size: int
    pages: int


# 品种模式
class BreedBase(BaseModel):
    """品种基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="品种名称")
    species: str = Field(..., min_length=1, max_length=50, description="物种")
    description: Optional[str] = Field(None, description="品种描述")
    origin_country: Optional[str] = Field(None, max_length=100, description="原产国")
    average_weight_min: Optional[float] = Field(None, ge=0, description="平均体重最小值(kg)")
    average_weight_max: Optional[float] = Field(None, ge=0, description="平均体重最大值(kg)")
    average_height_min: Optional[float] = Field(None, ge=0, description="平均身高最小值(cm)")
    average_height_max: Optional[float] = Field(None, ge=0, description="平均身高最大值(cm)")
    life_expectancy: Optional[int] = Field(None, ge=0, le=50, description="预期寿命(年)")
    temperament: Optional[str] = Field(None, description="性格特征")
    care_requirements: Optional[str] = Field(None, description="护理要求")


class BreedCreate(BreedBase):
    """创建品种模式"""
    pass


class BreedUpdate(BaseModel):
    """更新品种模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="品种名称")
    species: Optional[str] = Field(None, min_length=1, max_length=50, description="物种")
    description: Optional[str] = Field(None, description="品种描述")
    origin_country: Optional[str] = Field(None, max_length=100, description="原产国")
    average_weight_min: Optional[float] = Field(None, ge=0, description="平均体重最小值(kg)")
    average_weight_max: Optional[float] = Field(None, ge=0, description="平均体重最大值(kg)")
    average_height_min: Optional[float] = Field(None, ge=0, description="平均身高最小值(cm)")
    average_height_max: Optional[float] = Field(None, ge=0, description="平均身高最大值(cm)")
    life_expectancy: Optional[int] = Field(None, ge=0, le=50, description="预期寿命(年)")
    temperament: Optional[str] = Field(None, description="性格特征")
    care_requirements: Optional[str] = Field(None, description="护理要求")


class Breed(BreedBase):
    """品种响应模式"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    created_at: datetime
    updated_at: datetime


class BreedList(BaseModel):
    """品种列表响应模式"""
    items: List[Breed]
    total: int
    page: int
    size: int
    pages: int
