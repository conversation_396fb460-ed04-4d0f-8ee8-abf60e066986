"""
宠物管理API端点 - 统一响应格式
"""
from typing import Optional

from fastapi import APIRouter, Depends, Query, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from shared.database import get_async_db
from shared.utils.auth import get_current_active_user, get_current_user_optional
from shared.models.response import (
    SuccessResponse, PaginatedResponse, create_success_response,
    create_paginated_response, ErrorCode, SuccessMessage
)
from shared.middleware.request_id import get_request_id
from shared.utils.exception_handlers import BusinessException

from ..schemas.pet import (
    Pet as PetSchema,
    PetCreate,
    PetUpdate
)
from ..services.pet_service import PetService

router = APIRouter()


@router.get("/", response_model=PaginatedResponse[PetSchema])
async def get_pets(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    species: Optional[str] = Query(None, description="物种过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    owner_id: Optional[int] = Query(None, description="主人ID过滤（仅管理员）"),
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取宠物列表"""

    # 非管理员不能使用owner_id过滤
    if owner_id and not current_user.get('is_superuser', False):
        owner_id = None

    pets, total = await PetService.get_pets(
        db=db,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser', False),
        page=page,
        size=size,
        search=search,
        species=species,
        status=status,
        owner_id=owner_id
    )

    # 转换为Pydantic模型
    pet_items = [PetSchema.model_validate(pet) for pet in pets]

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一分页响应格式
    return create_paginated_response(
        items=pet_items,
        total=total,
        page=page,
        size=size,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.get("/{pet_id}", response_model=SuccessResponse[PetSchema])
async def get_pet(
    request: Request,
    pet_id: int,
    current_user=Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_async_db)
):
    """获取宠物详情"""

    pet = await PetService.get_pet_by_id(
        db=db,
        pet_id=pet_id,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser', False)
    )

    if not pet:
        raise BusinessException(
            message="宠物不存在或无权限访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    pet_data = PetSchema.model_validate(pet)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=pet_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )


@router.post("/", response_model=SuccessResponse[PetSchema], status_code=status.HTTP_201_CREATED)
async def create_pet(
    request: Request,
    pet_create: PetCreate,
    current_user=Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_async_db)
):
    """创建宠物"""

    # 非管理员只能为自己创建宠物
    if not current_user.get('is_superuser', False) and pet_create.owner_id != current_user.get('id'):
        raise BusinessException(
            message="只能为自己创建宠物",
            error_code=ErrorCode.FORBIDDEN,
            status_code=status.HTTP_403_FORBIDDEN
        )

    try:
        pet = await PetService.create_pet(
            db=db,
            pet_data=pet_create,
            current_user_id=current_user.get('id')
        )
    except Exception as e:
        raise BusinessException(
            message="创建宠物失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    # 转换为Pydantic模型
    pet_data = PetSchema.model_validate(pet)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=pet_data,
        msg=SuccessMessage.CREATED,
        request_id=request_id
    )


@router.put("/{pet_id}", response_model=SuccessResponse[PetSchema])
async def update_pet(
    request: Request,
    pet_id: int,
    pet_update: PetUpdate,
    current_user=Depends(get_current_user_optional),
    db: AsyncSession = Depends(get_async_db)
):
    """更新宠物信息"""

    try:
        pet = await PetService.update_pet(
            db=db,
            pet_id=pet_id,
            pet_update=pet_update,
            current_user_id=current_user.get('id'),
            is_superuser=current_user.get('is_superuser', False)
        )
    except Exception as e:
        raise BusinessException(
            message="更新宠物失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    if not pet:
        raise BusinessException(
            message="宠物不存在或无权限访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 转换为Pydantic模型
    pet_data = PetSchema.model_validate(pet)

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=pet_data,
        msg=SuccessMessage.UPDATED,
        request_id=request_id
    )


@router.delete("/{pet_id}", response_model=SuccessResponse[None])
async def delete_pet(
    request: Request,
    pet_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """删除宠物"""

    try:
        success = await PetService.delete_pet(
            db=db,
            pet_id=pet_id,
            current_user_id=current_user.get('id'),
            is_superuser=current_user.get('is_superuser', False)
        )
    except Exception as e:
        raise BusinessException(
            message="删除宠物失败",
            error_code=ErrorCode.OPERATION_FAILED,
            details={"original_error": str(e)}
        )

    if not success:
        raise BusinessException(
            message="宠物不存在或无权限访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式（无数据）
    return create_success_response(
        data=None,
        msg=SuccessMessage.DELETED,
        request_id=request_id
    )


@router.get("/{pet_id}/stats", response_model=SuccessResponse[dict])
async def get_pet_stats(
    request: Request,
    pet_id: int,
    current_user=Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """获取宠物统计信息"""

    # 检查宠物权限
    pet = await PetService.get_pet_by_id(
        db=db,
        pet_id=pet_id,
        current_user_id=current_user.get('id'),
        is_superuser=current_user.get('is_superuser', False)
    )

    if not pet:
        raise BusinessException(
            message="宠物不存在或无权限访问",
            error_code=ErrorCode.NOT_FOUND,
            status_code=status.HTTP_404_NOT_FOUND
        )

    # 获取统计信息
    from sqlalchemy import select, func
    from ..models.pet import MedicalRecord, Vaccination

    # 医疗记录数量
    medical_count_result = await db.execute(
        select(func.count(MedicalRecord.id)).where(
            MedicalRecord.pet_id == pet_id)
    )
    medical_count = medical_count_result.scalar()

    # 疫苗接种数量
    vaccination_count_result = await db.execute(
        select(func.count(Vaccination.id)).where(Vaccination.pet_id == pet_id)
    )
    vaccination_count = vaccination_count_result.scalar()

    # 最近就诊日期
    latest_visit_result = await db.execute(
        select(func.max(MedicalRecord.visit_date)).where(
            MedicalRecord.pet_id == pet_id)
    )
    latest_visit = latest_visit_result.scalar()

    # 最近疫苗接种日期
    latest_vaccination_result = await db.execute(
        select(func.max(Vaccination.vaccination_date)).where(
            Vaccination.pet_id == pet_id)
    )
    latest_vaccination = latest_vaccination_result.scalar()

    stats_data = {
        "pet_id": pet_id,
        "medical_records_count": medical_count,
        "vaccinations_count": vaccination_count,
        "latest_visit_date": latest_visit,
        "latest_vaccination_date": latest_vaccination,
        "age_display": pet.age_display
    }

    # 获取请求ID
    request_id = get_request_id(request)

    # 返回统一成功响应格式
    return create_success_response(
        data=stats_data,
        msg=SuccessMessage.RETRIEVED,
        request_id=request_id
    )
